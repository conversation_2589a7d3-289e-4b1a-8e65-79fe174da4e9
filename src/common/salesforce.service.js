const axios = require("axios");
const {
  SecretsManagerClient,
  GetSecretValueCommand,
  UpdateSecretCommand,
  CreateSecretCommand,
} = require("@aws-sdk/client-secrets-manager");

secretClient = new SecretsManagerClient({
  region: process.env.REGION,
});
const createAWSSecret = async (secretName, secretValue) => {
  try {
    await secretClient.send(
      new CreateSecretCommand({
        Name: secretName,
        SecretString: JSON.stringify(secretValue),
      })
    );
  } catch (error) {
    console.error("Error creating secret:", error);
  }
};

const getAWSSecret = async (secretName) => {
  try {
    const secretValue = await secretClient.send(
      new GetSecretValueCommand({
        SecretId: secretName,
      })
    );

    if (secretValue.SecretString) {
      return JSON.parse(secretValue.SecretString);
    } else {
      console.log("there is no secret Key");
    }
  } catch (error) {
    console.error("Error retrieving secret:", error);
  }
};

const updateSecret = async (secretName, newSecretValue) => {
  try {
    await secretClient.send(
      new UpdateSecretCommand({
        SecretId: secretName,
        SecretString: JSON.stringify(newSecretValue),
      })
    );
  } catch (error) {
    const errorType = error?.__type;

    if (errorType === "ResourceNotFoundException") {
      createAWSSecret(secretName, newSecretValue);
    }
    console.error(`Error updating secret '${secretName}':`, error);
  }
};

const getCredentials = async (isTokenExpired = false) => {
  let response;
  // const secretKeyResponse = await getAWSSecret(process.env.ACCESS_TOKEN_SECRET);
  if (true) {
    response = await axios.post(process.env.GUS_AUTH_URL, null, {
      params: {
        grant_type: process.env.GUS_GRANT_TYPE,
        client_id: process.env.GUS_CONSUMER_KEY,
        client_secret: process.env.GUS_CONSUMER_SECRET,
        username: process.env.GUS_USER_NAME,
        password: process.env.GUS_PASSWORD,
      },
    });

    // await updateSecret(process.env.ACCESS_TOKEN_SECRET, response.data);

    return response.data;
  }
  return secretKeyResponse;
};

module.exports.executeAPI = async (
  apiPath,
  method,
  payload = null,
  isTotalRequired = false,
  isQuery = true
) => {
  const axiosConfig = await buildAxiosConfig(apiPath, method, payload, isQuery);
  try {
    console.log(axiosConfig);
    const response = await axios(axiosConfig);

    if (response?.data?.nextRecordsUrl) {
      const nextResponse = await this.executeAPI(
        `query/${response.data.nextRecordsUrl.split("/").pop()}`,
        "GET",
        null,
        false,
        false
      );
      return [...response.data.records, ...nextResponse];
    }

    if (isTotalRequired) {
      return response.data;
    } else if (method === "GET") {
      return response.data.records;
    }
    return response;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error;
      if (axiosError.response && axiosError.response.status === 401) {
        console.log("Got a 401 Unauthorized error. Refreshing access token...");
        await getCredentials(true);
        return this.executeAPI(apiPath, method, payload);
      }
      if (axiosError.response) {
        console.error("Server responded with error:", axiosError.response.data);
        console.error("HTTP status code:", axiosError.response.status);
        throw new InternalServerErrorException(axiosError.response.data);
      }
    } else if (error.request) {
      console.error("No response received from the server");
      throw new InternalServerErrorException(
        "No response received from the server"
      );
    } else {
      console.error("Error setting up the request:", error.message);
      throw new InternalServerErrorException("Error setting up the request");
    }
    throw error;
  }
};

module.exports.compositeRequest = async (requests) => {
  const compositeRequestPayload = {};
  compositeRequestPayload["allOrNone"] = false;
  compositeRequestPayload["compositeRequest"] = requests;
  const response = await this.executeAPI(
    "composite",
    "POST",
    compositeRequestPayload,
    true
  );
  for (compResponse of response?.compositeResponse) {
    if (compResponse?.body?.nextRecordsUrl) {
      const nextResponse = await this.executeAPI(
        `query/${compResponse?.body?.nextRecordsUrl.split("/").pop()}`,
        "GET",
        null,
        false,
        false
      );
      compResponse.body.records = [
        ...compResponse.body.records,
        ...nextResponse,
      ];
    }
  }
  return response;
};

const buildAxiosConfig = async (endpoint, method, payload, isQuery) => {
  const clientCredentials = await getCredentials();
  const rootPath = `${clientCredentials.instance_url}/services/data/${process.env.SALESFORCE_API_VERSION}/`;
  switch (method) {
    case "POST":
    case "PATCH":
      return {
        url: `${rootPath}${endpoint}`,
        method,
        headers: {
          Authorization: `Bearer ${clientCredentials.access_token}`,
        },
        data: payload,
      };
    case "DELETE":
      return {
        url: `${rootPath}${endpoint}`,
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${clientCredentials.access_token}`,
        },
      };
    case "GET":
      if (isQuery) {
        return {
          url: `${rootPath}query?q=${endpoint}`,
          method: "GET",
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
          },
        };
      } else {
        return {
          url: `${rootPath}${endpoint}`,
          method: "GET",
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
          },
        };
      }
  }
};

module.exports.postData = async (
  endpoint,
  requestData,
  correlationId = null,
  apiKey = null,
  method = null
) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
    console.log("Path ->", path);
    const headers = {
      "Content-Type": "application/json",
      "x-api-key": apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    console.log("headers ->", headers);
    console.log("requestData -->", JSON.stringify(requestData));
    if (correlationId) {
      headers["Correlation-Id"] = correlationId;
    }

    const response = await fetch(path, {
      method: method ? method : "POST",
      headers: headers,
      body: JSON.stringify(requestData),
    });

    console.log("Response -->", response);
    if (!response.ok) {
      const errorResponse = await response.text();
      console.log("Error in response -->", errorResponse);
      throw errorResponse;
    }
    let responseData;
    const contentLength = response.headers.get("content-length");

    if (contentLength && parseInt(contentLength) > 0) {
      responseData = await response.json();
    } else {
      responseData = null;
    }
    console.log("responseData", responseData);
    if (Array.isArray(responseData)) {
      console.log("response is an array");
      return responseData;
    } else {
      console.log("response is not a array");
      return responseData;
    }
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

module.exports.getData = async (
  endpoint,
  correlationId = null,
  apiKey = null
) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
    const headers = {
      "x-api-key": apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    if (correlationId) {
      headers["Correlation-Id"] = correlationId;
    }
    const response = await axios.get(path, {
      headers: headers,
    });
    return await response.data;
  } catch (error) {
    console.log("Err", error);
    throw error?.response?.data;
  }
};
